/**
 * utils/date-utils.js
 *
 * Purpose: Date manipulation and formatting utilities for subscription management.
 * Handles date parsing, formatting, timezone conversions, and billing cycle calculations.
 *
 * Key features:
 * - Safe date parsing with error handling
 * - Locale-aware date formatting
 * - Timezone-based date adjustments
 * - Billing cycle calculations
 * - Next payment date computation
 * - Relative date formatting ("in X days")
 * - Multi-locale support (EN, FR, ES, etc.)
 * - Date comparison utilities
 */

import {
  format,
  intlFormatDistance,
  parseISO,
  endOfDay,
  startOfDay,
  startOfToday,
  differenceInCalendarDays,
  addDays,
  addMonths,
  addWeeks,
  addYears,
} from "date-fns";
import {
  enUS,
  enGB,
  enCA,
  fr,
  frCA,
  es,
  de,
  it,
  nl,
  ja,
} from "date-fns/locale";

// Shared locale configuration
export const LOCALES = {
  // North American
  "en-US": enUS,
  "en-CA": enCA,
  "fr-CA": frCA,
  "es-MX": es,

  // European
  "en-GB": enGB,
  "fr-FR": fr,
  "es-ES": es,
  "de-DE": de,
  "it-IT": it,
  "nl-NL": nl,

  // Asian
  "ja-JP": ja,
};

// Date type configurations
const END_OF_DAY_DATES = [
  "trial_end_date",
  "cancel_date",
  "expiry_date",
  "promo_end_date",
  "discount_end_date",
];

const START_OF_DAY_DATES = [
  "payment_date",
  "next_payment_date",
  "trial_start_date",
  "renewal_date",
  "last_paid_date",
];

/**
 * Gets the default locale based on browser settings
 */
export function getDefaultLocale() {
  if (typeof navigator === "undefined") return "en-US";

  const browserLocale = navigator.language;
  if (LOCALES[browserLocale]) return browserLocale;

  const languagePart = browserLocale.split("-")[0];
  const match = Object.keys(LOCALES).find((locale) =>
    locale.startsWith(`${languagePart}-`)
  );

  return match || "en-US";
}

/**
 * Safely parses a date string into a Date object
 */
export function parseDateSafely(dateString) {
  if (!dateString) return null;

  try {
    if (typeof dateString === "string") {
      // Handle ISO format with time
      if (dateString.includes("T")) {
        return parseISO(dateString);
      }

      // Handle YYYY-MM format
      if (dateString.match(/^\d{4}-\d{2}$/)) {
        const [year, month] = dateString.split("-").map(Number);
        return new Date(year, month - 1, 1);
      }

      // Handle YYYY-MM-DD format
      if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const [year, month, day] = dateString.split("-").map(Number);
        return new Date(year, month - 1, day);
      }
    }

    if (dateString instanceof Date) {
      return isNaN(dateString.getTime()) ? null : dateString;
    }

    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error("Date parsing error:", error);
    return null;
  }
}

/**
 * Formats a date with locale support
 */
export function formatDate(
  date,
  formatOptions = "PPP",
  localeCode = "en-US",
  options = {}
) {
  try {
    if (!date) return "";

    const parsedDate = date instanceof Date ? date : parseDateSafely(date);
    if (!parsedDate) return "";

    // Handle UTC midnight adjustment
    const isUTCMidnight =
      parsedDate.getUTCHours() === 0 &&
      parsedDate.getUTCMinutes() === 0 &&
      parsedDate.getUTCSeconds() === 0;

    let adjustedDate = parsedDate;
    if (isUTCMidnight) {
      adjustedDate = new Date(
        parsedDate.getUTCFullYear(),
        parsedDate.getUTCMonth(),
        parsedDate.getUTCDate(),
        0,
        0,
        0
      );
    }

    // Apply end/start of day adjustments
    adjustedDate =
      options?.fieldName ?
        END_OF_DAY_DATES.includes(options.fieldName) ? endOfDay(adjustedDate)
          : START_OF_DAY_DATES.includes(options.fieldName) ?
            startOfDay(adjustedDate)
            : adjustedDate
        : adjustedDate;

    // If formatOptions is already an Intl.DateTimeFormat options object, use it directly
    if (typeof formatOptions === "object") {
      const formatter = new Intl.DateTimeFormat(localeCode, formatOptions);
      return formatter.format(adjustedDate);
    }

    // Convert date-fns format strings to Intl.DateTimeFormat options
    const formatMap = {
      PPP: {
        // "April 29th, 1453"
        year: "numeric",
        month: "long",
        day: "numeric",
      },
      MMMM: { month: "long" },
      "MMM d, yyyy": {
        year: "numeric",
        month: "short",
        day: "numeric",
      },
      "EEEE, do": {
        weekday: "long",
        day: "numeric",
      },
      // Add other format patterns as needed
    };

    if (formatMap[formatOptions]) {
      const formatter = new Intl.DateTimeFormat(
        localeCode,
        formatMap[formatOptions]
      );
      let formattedDate = formatter.format(adjustedDate);

      // Handle ordinal indicators if needed (for 'do' patterns)
      if (formatOptions.includes("do")) {
        const day = adjustedDate.getDate();
        const ordinal = getOrdinalSuffix(day, localeCode);
        formattedDate = formattedDate.replace(/\d+/, `${day}${ordinal}`);
      }

      return formattedDate;
    }

    // Fallback to date-fns for unsupported formats
    const dateLocale = LOCALES[localeCode] || LOCALES["en-US"];
    return format(adjustedDate, formatOptions, { locale: dateLocale });
  } catch (error) {
    console.error("Date formatting error:", error);
    return "";
  }
}

// Helper function for ordinal suffixes with basic localization
function getOrdinalSuffix(n, locale) {
  if (locale.startsWith("en")) {
    const s = ["th", "st", "nd", "rd"];
    const v = n % 100;
    return s[(v - 20) % 10] || s[v] || s[0];
  }
  // Add other locale-specific ordinal rules as needed
  return "";
}

/**
 * Gets subscription days remaining
 */
export function getSubscriptionDaysLeft(endDate, locale = "en-US") {
  if (!endDate)
    return { daysLeft: 0, timeLeftHuman: "", endDate: null, endsAt: null };

  try {
    const parsedEndDate = parseDateSafely(endDate);
    if (!parsedEndDate)
      return { daysLeft: 0, timeLeftHuman: "", endDate: null, endsAt: null };

    const endDateTime = endOfDay(parsedEndDate);
    const startTime = startOfToday();

    const daysLeft = differenceInCalendarDays(endDateTime, startTime);
    const timeLeftHuman = intlFormatDistance(endDateTime, startTime, {
      addSuffix: false,
      locale: LOCALES[locale],
    });

    return {
      daysLeft,
      timeLeftHuman,
      endDate: parsedEndDate,
      endsAt: endDateTime,
    };
  } catch (error) {
    console.error("Subscription days calculation error:", error);
    return { daysLeft: 0, timeLeftHuman: "", endDate: null, endsAt: null };
  }
}

/**
 * Additional utility functions
 */
export function isTrialActive(endDate) {
  if (!endDate) return false;
  const endDateTime = endOfDay(parseISO(endDate));
  return endDateTime >= new Date();
}

export function calculateDiscountEndDate(startDate, subscriptionType, cycles) {
  if (!startDate || !subscriptionType || !cycles) return null;
  const start = parseDateSafely(startDate);
  if (!start) return null;

  // Ensure we're working with integers
  const numCycles = parseInt(cycles, 10);
  if (isNaN(numCycles) || numCycles <= 0) return null;

  const billingInterval =
    subscriptionType.billing_interval?.toLowerCase() ||
    subscriptionType.name?.toLowerCase() ||
    "";

  // Handle different billing intervals
  if (billingInterval.includes("month")) {
    return addMonths(start, numCycles);
  } else if (
    billingInterval.includes("year") ||
    billingInterval.includes("annual")
  ) {
    return addYears(start, numCycles);
  } else if (billingInterval.includes("week")) {
    return addWeeks(start, numCycles);
  } else if (billingInterval.includes("quarter")) {
    return addMonths(start, numCycles * 3);
  } else {
    // Default to days if specified, otherwise return null
    const days = subscriptionType.days;
    return days ? addDays(start, days * numCycles) : null;
  }
}

export function formatDiscountEndMessage(discountEnd, promoEnd) {
  const messages = [];

  if (promoEnd) {
    const promoEndDate = format(promoEnd, "PPP");
    messages.push(`Promotional price ends on ${promoEndDate}`);
  }

  if (discountEnd) {
    const discountEndDate = format(discountEnd, "PPP");
    messages.push(`Personal discount ends on ${discountEndDate}`);
  }

  return messages.join(" • ");
}

// export const getNextDay = (date) =>
//   date ? addDays(parseDateSafely(date), 1) : null;
