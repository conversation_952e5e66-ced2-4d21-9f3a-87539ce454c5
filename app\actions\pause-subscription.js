/**
 * app/actions/pause-subscription.js
 * 
 * Purpose: Server action for pausing subscriptions.
 * Handles subscription pausing with tier-based limits.
 * 
 * SECURITY: Verifies user owns the subscription before allowing pause
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { canUsePause } from "@/utils/checks";
import { FEATURES } from "@/utils/plan-utils";
import { formatISO } from "date-fns";

export async function pauseSubscription(
  subscriptionId,
  { pauseUntil, reason }
) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  const { data: subscription, error: fetchError } = await supabase
    .from("subscriptions")
    .select(
      `
      *,
      profiles:user_id (
        pricing_tier,
        is_admin
      )
    `
    )
    .eq("id", subscriptionId)
    .eq("user_id", user.id) // CRITICAL: Verify ownership
    .is("deleted_at", null)
    .single();

  if (fetchError || !subscription) {
    throw new Error("Subscription not found or access denied");
  }

  if (!canUsePause(subscription.profiles)) {
    throw new Error("Your plan does not include subscription pausing");
  }

  const today = formatISO(new Date(), {
    representation: "date",
  });
  const pauseEndDate = formatISO(new Date(pauseUntil), {
    representation: "date",
  });
  const userTier = subscription.profiles.pricing_tier.toLowerCase();

  // Get max pause duration from FEATURES definition
  const maxPauseDays = FEATURES.PAUSE_CONTROL.limits[userTier];
  const pauseDurationDays = Math.ceil(
    (pauseEndDate - today) / (1000 * 60 * 60 * 24)
  );

  if (pauseDurationDays > maxPauseDays) {
    throw new Error(
      `Maximum pause duration for your plan is ${maxPauseDays} days`
    );
  }

  const { error: updateError } = await supabase
    .from("subscriptions")
    .update({
      is_paused: true,
      pause_start_date: today.toISOString(),
      pause_end_date: pauseEndDate.toISOString(),
      pause_reason: reason,
      next_payment_date: new Date(subscription.next_payment_date).setDate(
        new Date(subscription.next_payment_date).getDate() + pauseDurationDays
      ),
    })
    .eq("id", subscriptionId)
    .eq("user_id", user.id); // Double ensure ownership

  if (updateError) throw updateError;

  return { success: true };
}